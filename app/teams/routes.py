# Team management routes - to be implemented
from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.teams import bp
from app.models import Team, Player, UserRole
from app import db


@bp.route('/')
def index():
    """List all teams."""
    return "Team list - to be implemented"


@bp.route('/create')
def create():
    """Create new team."""
    return "Create team - to be implemented"


@bp.route('/<int:id>')
def view(id):
    """View team details."""
    return f"Team {id} details - to be implemented"


@bp.route('/my-teams')
@login_required
def my_teams():
    """View teams where current user is captain or member."""
    if current_user.role != UserRole.PLAYER:
        flash('Only players can view team memberships.', 'error')
        return redirect(url_for('teams.index'))

    # Check if user has a player profile
    if not current_user.player_profile:
        flash('You need to complete your player profile first.', 'info')
        return redirect(url_for('auth.profile'))

    player = current_user.player_profile

    # Get teams where user is captain
    captain_teams = Team.query.filter_by(
        captain_id=player.id,
        is_active=True
    ).order_by(Team.created_at.desc()).all()

    # Get team where user is a member (if any)
    member_team = player.team if player.team and player.team.is_active else None

    return render_template('teams/my_teams.html',
                         title='My Teams',
                         captain_teams=captain_teams,
                         member_team=member_team)
