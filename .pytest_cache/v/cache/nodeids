["tests/test_auth.py::TestAuthProtection::test_admin_routes_require_admin", "tests/test_auth.py::TestAuthProtection::test_organizer_routes_require_organizer", "tests/test_auth.py::TestAuthProtection::test_protected_routes_redirect", "tests/test_auth.py::TestAuthRoutes::test_duplicate_username_registration", "tests/test_auth.py::TestAuthRoutes::test_invalid_login", "tests/test_auth.py::TestAuthRoutes::test_login_page_loads", "tests/test_auth.py::TestAuthRoutes::test_register_page_loads", "tests/test_auth.py::TestAuthRoutes::test_registration_validation", "tests/test_auth.py::TestAuthRoutes::test_user_login", "tests/test_auth.py::TestAuthRoutes::test_user_logout", "tests/test_auth.py::TestAuthRoutes::test_user_registration", "tests/test_auth.py::TestPasswordSecurity::test_password_hashing", "tests/test_auth.py::TestPasswordSecurity::test_password_requirements", "tests/test_auth.py::TestSessionManagement::test_logout_clears_session", "tests/test_auth.py::TestSessionManagement::test_session_persistence", "tests/test_auth.py::TestUserRoles::test_admin_access", "tests/test_auth.py::TestUserRoles::test_organizer_access", "tests/test_auth.py::TestUserRoles::test_player_access", "tests/test_models.py::TestMatchModel::test_match_creation", "tests/test_models.py::TestMatchModel::test_match_relationships", "tests/test_models.py::TestMatchModel::test_match_result_setting", "tests/test_models.py::TestModelValidation::test_enum_validation", "tests/test_models.py::TestModelValidation::test_required_fields", "tests/test_models.py::TestModelValidation::test_unique_constraints", "tests/test_models.py::TestPlayerModel::test_player_creation", "tests/test_models.py::TestPlayerModel::test_player_relationships", "tests/test_models.py::TestTeamModel::test_team_creation", "tests/test_models.py::TestTeamModel::test_team_relationships", "tests/test_models.py::TestTeamModel::test_team_repr", "tests/test_models.py::TestTournamentModel::test_tournament_creation", "tests/test_models.py::TestTournamentModel::test_tournament_relationships", "tests/test_models.py::TestTournamentModel::test_tournament_repr", "tests/test_models.py::TestTournamentModel::test_tournament_status_transitions", "tests/test_models.py::TestUserModel::test_password_hashing", "tests/test_models.py::TestUserModel::test_user_creation", "tests/test_models.py::TestUserModel::test_user_repr", "tests/test_services.py::TestBracketGenerator::test_basic_bracket_logic", "tests/test_services.py::TestBracketGenerator::test_bracket_generation_insufficient_teams", "tests/test_services.py::TestBracketGenerator::test_round_robin_bracket", "tests/test_services.py::TestBracketGenerator::test_single_elimination_bracket_4_teams", "tests/test_services.py::TestBracketGenerator::test_single_elimination_bracket_8_teams", "tests/test_services.py::TestBracketGenerator::test_tournament_teams_relationship", "tests/test_services.py::TestLeaderboardService::test_calculate_round_robin_leaderboard", "tests/test_services.py::TestLeaderboardService::test_calculate_single_elimination_leaderboard", "tests/test_services.py::TestLeaderboardService::test_leaderboard_with_no_matches", "tests/test_services.py::TestMatchScheduler::test_schedule_matches_basic", "tests/test_services.py::TestMatchScheduler::test_schedule_matches_different_rounds", "tests/test_services.py::TestMatchScheduler::test_schedule_matches_with_intervals", "tests/test_teams.py::TestTeamRoutes::test_my_teams_url_for_works", "tests/test_tournaments.py::TestTournamentBrackets::test_bracket_generation", "tests/test_tournaments.py::TestTournamentBrackets::test_bracket_page_loads", "tests/test_tournaments.py::TestTournamentBrackets::test_bracket_without_teams", "tests/test_tournaments.py::TestTournamentCreation::test_create_tournament_success", "tests/test_tournaments.py::TestTournamentCreation::test_create_tournament_validation", "tests/test_tournaments.py::TestTournamentManagement::test_edit_other_users_tournament", "tests/test_tournaments.py::TestTournamentManagement::test_tournament_delete", "tests/test_tournaments.py::TestTournamentManagement::test_tournament_edit_page", "tests/test_tournaments.py::TestTournamentManagement::test_tournament_edit_success", "tests/test_tournaments.py::TestTournamentManagement::test_unauthorized_tournament_edit", "tests/test_tournaments.py::TestTournamentRegistration::test_duplicate_team_registration", "tests/test_tournaments.py::TestTournamentRegistration::test_team_registration_page", "tests/test_tournaments.py::TestTournamentRegistration::test_team_registration_success", "tests/test_tournaments.py::TestTournamentRoutes::test_tournament_create_page_authenticated", "tests/test_tournaments.py::TestTournamentRoutes::test_tournament_create_page_requires_auth", "tests/test_tournaments.py::TestTournamentRoutes::test_tournament_detail_page", "tests/test_tournaments.py::TestTournamentRoutes::test_tournament_list_page", "tests/test_tournaments.py::TestTournamentStatus::test_tournament_start", "tests/test_tournaments.py::TestTournamentStatus::test_tournament_status_transitions"]