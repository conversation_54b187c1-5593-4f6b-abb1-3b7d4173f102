"""Test teams functionality."""
import pytest
from flask import url_for
from app.models import Use<PERSON>, Use<PERSON><PERSON><PERSON>, Team, Player
from app import db
from tests.conftest import login_user, logout_user


class TestTeamRoutes:
    """Test team routes."""
    
    def test_teams_index_loads(self, client):
        """Test teams index page loads correctly."""
        response = client.get('/teams/')
        assert response.status_code == 200
    
    def test_my_teams_requires_login(self, client):
        """Test my_teams route requires authentication."""
        response = client.get('/teams/my-teams')
        assert response.status_code == 302  # Redirect to login
    
    def test_my_teams_url_for_works(self, app):
        """Test that url_for('teams.my_teams') works without BuildError."""
        with app.app_context():
            url = url_for('teams.my_teams')
            assert url == '/teams/my-teams'
    
    def test_my_teams_with_player_user(self, client, app, player_user):
        """Test my_teams route with authenticated player user."""
        # Login as player
        login_user(client, 'player', 'password')
        
        # Access my_teams route
        response = client.get('/teams/my-teams')
        
        # Should redirect to profile if no player profile exists
        # or show the my_teams page if player profile exists
        assert response.status_code in [200, 302]
    
    def test_my_teams_with_non_player_user(self, client, app, organizer_user):
        """Test my_teams route with non-player user."""
        # Login as organizer
        login_user(client, 'organizer', 'password')
        
        # Access my_teams route
        response = client.get('/teams/my-teams', follow_redirects=True)
        
        # Should redirect with error message
        assert response.status_code == 200
        assert b'Only players can view team memberships' in response.data or b'Team list' in response.data


class TestTeamModels:
    """Test team model functionality."""
    
    def test_team_creation(self, app):
        """Test team creation."""
        with app.app_context():
            # Create a user and player first
            user = User(
                username='testcaptain',
                email='<EMAIL>',
                first_name='Test',
                last_name='Captain',
                role=UserRole.PLAYER
            )
            user.set_password('password')
            db.session.add(user)
            db.session.commit()
            
            # Create player profile
            player = Player(
                user_id=user.id,
                in_game_name='TestCaptain'
            )
            db.session.add(player)
            db.session.commit()
            
            # Create team
            team = Team(
                name='Test Team',
                tag='TEST',
                description='A test team',
                captain_id=player.id
            )
            db.session.add(team)
            db.session.commit()
            
            assert team.name == 'Test Team'
            assert team.tag == 'TEST'
            assert team.captain_id == player.id
            assert team.is_active is True
    
    def test_team_captain_relationship(self, app):
        """Test team captain relationship."""
        with app.app_context():
            # Create user and player
            user = User(
                username='testcaptain2',
                email='<EMAIL>',
                first_name='Test',
                last_name='Captain2',
                role=UserRole.PLAYER
            )
            user.set_password('password')
            db.session.add(user)
            db.session.commit()
            
            player = Player(
                user_id=user.id,
                in_game_name='TestCaptain2'
            )
            db.session.add(player)
            db.session.commit()
            
            # Create team
            team = Team(
                name='Test Team 2',
                tag='TEST2',
                captain_id=player.id
            )
            db.session.add(team)
            db.session.commit()
            
            # Test relationships
            assert team.captain == player
            assert team.captain.user == user
